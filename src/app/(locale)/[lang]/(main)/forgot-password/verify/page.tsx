"use client"

import { useEffect, useState } from "react"
import { useSearchPara<PERSON>, useRouter } from "next/navigation"
import { <PERSON><PERSON>, <PERSON><PERSON> } from "@kickavenue/ui/components"

import PasswordInput from "@app/components/InputPassword"

import { useForgotPasswordForm } from "../hooks/useForgotPasswordForm"
import { useTokenVerification } from "../hooks/useTokenVerification"
import { usePasswordReset } from "../hooks/usePasswordReset"

const ForgotPassword = () => {
  const searchParams = useSearchParams()
  const token = searchParams.get("token")
  const emailParam = searchParams.get("email")

  const { email } = useForgotPasswordForm(emailParam)
  const [password, setPassword] = useState("")
  const [passwordConfirmation, setPasswordConfirmation] = useState("")

  const {
    isTokenVerified,
    isLoading: tokenLoading,
    isError: isTokenError,
  } = useTokenVerification(token, emailParam)
  const { resetPassword, isLoading } = usePasswordReset()

  const handlePasswordChange = (value: string) => {
    setPassword(value)
  }

  const handlePasswordConfirmationChange = (value: string) => {
    setPasswordConfirmation(value)
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    if (isTokenVerified) {
      await resetPassword(email, password, passwordConfirmation, token || "")
    }
  }

  const router = useRouter()

  useEffect(() => {
    if (isTokenError && !isTokenVerified)
      router.push("/forgot-password/token-expired")
  }, [isTokenError, isTokenVerified])

  return (
    <div className="mx-auto mb-80 mt-8 max-w-md">
      <div className="mb-8 text-center">
        <Heading heading="4" textStyle="bold">
          Reset Password
        </Heading>
      </div>
      <form onSubmit={handleSubmit}>
        <PasswordInput
          value={password}
          onChange={handlePasswordChange}
          label="New Password"
          className="mt-2"
          showRequirements
        />
        <PasswordInput
          value={passwordConfirmation}
          onChange={handlePasswordConfirmationChange}
          label="Confirm New Password"
          className="mt-2"
          showRequirements={false}
        />
        <div className="mt-8 w-full">
          <Button
            size="lg"
            variant="primary"
            className="!w-full"
            type="submit"
            disabled={isLoading || tokenLoading}
          >
            Reset Password
          </Button>
        </div>
      </form>
    </div>
  )
}

export default ForgotPassword
