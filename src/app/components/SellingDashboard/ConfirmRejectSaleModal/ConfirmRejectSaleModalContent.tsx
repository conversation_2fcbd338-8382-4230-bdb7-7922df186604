import { Divider, Space } from "@kickavenue/ui/dist/src/components"
import TTextProps from "@kickavenue/ui/dist/src/components/Text/Text.type"

import SpinnerLoading from "@components/shared/SpinnerLoading"
import ModalActionSpacer from "@components/shared/ModalParts/ModalActionSpacer"
import ModalActionTimeInfo from "@components/shared/ModalParts/ModalActionTimeInfo"
import ModalProduct from "@components/shared/ModalParts/ModalProduct"
import ModalUniqueID from "@components/shared/ModalParts/ModalUniqueID"
import useFetchSellerListingById from "@app/hooks/useFetchSellerListingById"
import useFetchSaleDetail from "@components/shared/SaleDetailModal/hook/useFetchSaleDetail"
import ModalCancelConfirm from "@components/shared/ModalCancelConfirm"
import { useModalStore } from "stores/modalStore"
import { ModalConstant } from "@constants/modal"
import { formatPrice, formatStripePrice, getStripAmount } from "@utils/misc"
import ModalSellingSummary from "@components/shared/ModalParts/ModalSellingSummary"
import ModalSummaryItem from "@components/shared/ModalParts/ModalSummaryItem"
import ModalSummaryTotal from "@components/shared/ModalParts/ModalSummaryTotal"
import { TOrderStatusTrack } from "types/transactionDetail.type"
import { useMemberStore } from "stores/memberStore"
import useGetPlatformFee from "@app/hooks/useGetPlatformFee"

const {
  CONFIRM_REJECT_SALE,
  CONFIRM_SALE_CONFIRM,
  CONFIRM_REJECT_SALE_CONFIRM,
} = ModalConstant.MODAL_IDS

const ConfirmRejectSaleModalContent = () => {
  const { setOpen } = useModalStore()
  const { member } = useMemberStore()

  const { data } = useFetchSaleDetail()
  const { data: listing, isLoading } = useFetchSellerListingById(
    data?.sellerListingId,
  )
  const { getMyPlatformFee } = useGetPlatformFee({
    memberId: member?.id,
    categoryId: listing?.item?.category?.id,
  })

  const sumPlatformFee =
    Number(data?.price.amountText) * (getMyPlatformFee()?.fee || 0)
  const totalSalesRevenue = getStripAmount(data?.price) - sumPlatformFee

  const summaries = [
    {
      text: "Sold Price",
      value: formatStripePrice(data?.price),
    },
    {
      text: `Platform Fee (${(getMyPlatformFee()?.fee as number) * 100}%)`,
      value: formatPrice(sumPlatformFee, null, "IDR"),
      valueProps: { state: "danger" } as Partial<TTextProps>,
      isMinus: true,
    },
  ]

  const handleConfirm = () => {
    setOpen(false, CONFIRM_REJECT_SALE)
    setOpen(true, CONFIRM_SALE_CONFIRM)
  }

  const handleReject = () => {
    setOpen(false, CONFIRM_REJECT_SALE)
    setOpen(true, CONFIRM_REJECT_SALE_CONFIRM)
  }

  if (isLoading) {
    return <SpinnerLoading className="min-w-[350px]" />
  }

  return (
    <>
      <div className="max-h-[331px] overflow-y-auto px-lg pb-lg">
        <ModalActionTimeInfo
          order={data}
          status={TOrderStatusTrack.OrderCreated}
        />
        <ModalActionSpacer />
        <ModalProduct listing={listing} />
        <Space size="sm" direction="y" type="margin" />
        <ModalUniqueID text="Order ID" value={data?.invoiceNumber || "-"} />
        <Space size="lg" direction="y" type="margin" />
        <ModalSellingSummary>
          {summaries.map((summary) => (
            <ModalSummaryItem key={summary.text} {...summary} />
          ))}
          <Divider orientation="horizontal" />
          <ModalSummaryTotal
            text="Total Revenue Sales"
            value={formatPrice(totalSalesRevenue, null, "IDR")}
          />
        </ModalSellingSummary>
      </div>
      <ModalCancelConfirm
        cancelText="Reject"
        onCancel={handleReject}
        onConfirm={handleConfirm}
        disableConfirm={isLoading}
      />
    </>
  )
}

export default ConfirmRejectSaleModalContent
