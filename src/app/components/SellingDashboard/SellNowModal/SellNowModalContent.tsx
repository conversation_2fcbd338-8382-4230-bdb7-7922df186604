import { Divider, Space } from "@kickavenue/ui/dist/src/components"
import TTextProps from "@kickavenue/ui/dist/src/components/Text/Text.type"

import ModalProduct from "@components/shared/ModalParts/ModalProduct"
import ModalSummaryTotal from "@components/shared/ModalParts/ModalSummaryTotal"
import SpinnerLoading from "@components/shared/SpinnerLoading"
import { SellerListing } from "types/sellerListing"
import { formatPrice, getStripAmount } from "@utils/misc"
import ModalSellingSummary from "@components/shared/ModalParts/ModalSellingSummary"
import ModalSummaryItem from "@components/shared/ModalParts/ModalSummaryItem"
import { useMemberStore } from "stores/memberStore"
import {
  calculatePlatformFee,
  getPlatformFeePercentage,
} from "@utils/fee.utils"
import useGetPlatformFee from "@app/hooks/useGetPlatformFee"

const SellNowModalContent = ({
  listing,
  isLoading,
}: {
  listing?: SellerListing
  isLoading: boolean
}) => {
  const { member } = useMemberStore()
  const { getMyPlatformFee } = useGetPlatformFee({
    memberId: member?.id,
    categoryId: listing?.item?.category?.id,
  })

  const offerAmount = getStripAmount(listing?.highestOfferAmount) || 0
  const calcFee = calculatePlatformFee(offerAmount, getMyPlatformFee())

  const summaries = [
    {
      text: "Offer Price",
      value: formatPrice(offerAmount, null, "IDR"),
    },
    {
      text: `Platform Fee (${getPlatformFeePercentage(getMyPlatformFee())}%)`,
      value: formatPrice(calcFee, null, "IDR"),
      isMinus: true,
      valueProps: { state: "danger" } as Partial<TTextProps>,
    },
  ]

  if (isLoading) {
    return <SpinnerLoading className="max-h-[331px] min-w-[350px]" />
  }

  return (
    <div className="max-h-[331px] overflow-y-auto px-lg">
      <ModalProduct listing={listing} />
      <Space size="lg" direction="y" type="margin" />
      <ModalSellingSummary>
        {summaries.map((summary) => (
          <ModalSummaryItem key={summary.text} {...summary} />
        ))}
        <Divider orientation="horizontal" />
        <ModalSummaryTotal
          text="Total Revenue Sales"
          value={formatPrice(offerAmount - calcFee, null, "IDR")}
        />
      </ModalSellingSummary>
    </div>
  )
}

export default SellNowModalContent
