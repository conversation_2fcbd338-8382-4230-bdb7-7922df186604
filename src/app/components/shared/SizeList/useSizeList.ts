import { useCallback, useEffect, useState } from "react"

import { useProductStore } from "stores/productStore"
import { TCountry } from "types/country.type"
import { TItemListing } from "types/itemListing.type"
import { SizeListProps } from "types/misc.type"
import { getItemConditionFromListing } from "@utils/itemListing"
import { TItemCondition } from "types/listing.type"
import useCheckoutPreviewStore from "stores/useCheckoutPreviewStore"
import { concatSizePrice, getDefaultSelectedSize } from "@utils/size"

const useSizeList = ({ defaultSelected }: SizeListProps) => {
  const [hoveredSize, setHoveredSize] = useState<Record<string, boolean>>(
    {} as Record<string, boolean>,
  )
  const {
    detail: product,
    setProductItemListing,
    setShowSizeSelection,
    setSelectedSize,
    setSelectedProductType,
    selectedProductType: productType,
    productDetailState,
    selectedSize,
    sizeType,
    setSizeType,
  } = useProductStore()

  const country = product?.country || ({} as TCountry)
  const handleChipClick = (listing: TItemListing) => {
    const sizeid = listing?.size?.id
    setProductItemListing(listing)
    const key = concatSizePrice({ listing, productType })
    setSelectedSize({ [key]: true })
    setShowSizeSelection(false)
    useCheckoutPreviewStore.getState().setCheckoutData({
      itemId: product.id ? product.id : 0,
      sizeId: sizeid ? sizeid : 0,
      sizeChartId: listing.size.sizeChartId,
    })
  }

  const handleChipHovered = (isHovered: boolean, listing: TItemListing) => {
    const key = concatSizePrice({ listing, productType })
    setHoveredSize({ [key]: isHovered })
  }

  const getChipState = (listing: TItemListing) => {
    const key = concatSizePrice({ listing, productType })
    if (selectedSize?.[key]) {
      return "pressed"
    }
    if (hoveredSize?.[key]) {
      return "hover"
    }
    return "default"
  }

  const getChipType = useCallback((listing: TItemListing) => {
    if (listing?.isConsignment || listing?.isConsigment) {
      return "badge-leading"
    }
    return listing?.lowestAsk ? "base" : "offer"
  }, [])

  useEffect(() => {
    if (!defaultSelected || Object.keys(selectedSize || {})?.length > 0) {
      return
    }
    const d = getDefaultSelectedSize(defaultSelected) as Record<string, boolean>
    const defProductType = getItemConditionFromListing(
      defaultSelected,
    ) as TItemCondition
    setSelectedSize(d)
    setSelectedProductType(defProductType)
  }, [
    defaultSelected,
    selectedSize,
    setSelectedSize,
    setSelectedProductType,
    productDetailState,
    productType,
  ])

  // reset selected size when component unmount
  useEffect(() => {
    return () => setSelectedSize({})
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [])

  return {
    country,
    hoveredSize,
    sizeType,
    handleChipClick,
    handleChipHovered,
    setSizeType,
    getChipState,
    getChipType,
    setSelectedSize,
  }
}

export default useSizeList
