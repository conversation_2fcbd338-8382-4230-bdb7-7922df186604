/* eslint-disable max-lines-per-function */
/* eslint-disable react-hooks/exhaustive-deps */
import React, { useState, useCallback } from "react"
import {
  Divider,
  IconDangerCircleBulk,
  IconInfoCircleBold,
  IconSuccessCircleBulk,
  IconWarningCircleBulk,
  InputQuantity,
  Text,
  TInputProps,
  Tooltip,
} from "@kickavenue/ui/dist/src/components"
import { useQuery } from "@tanstack/react-query"

import { formatPriceMinUnitVal, handleNumericInput } from "@utils/misc"
import Input from "@components/shared/Form/Input"
import { Isize } from "types/sizeChart.type"
import { formatCurrency, formatNumberWithSeparator } from "@utils/separator"
import { useSizeStore } from "stores/useSizeStore"
import { MemberApiRepository } from "@infrastructure/repositories/memberApiRepository"
import { useSellConsignmentStore } from "stores/sellConsignmentStore"
import useGetPlatformFee from "@app/hooks/useGetPlatformFee"

import useItemSummary from "./hooks/useItemSummary"

const ListSize = ({ size }: { size: Isize }) => {
  const [values, setValues] = useState<Record<string, string>>({})
  const { handlePriceChange, handleQuantityChange } = useSizeStore()
  const { selectedProduct, itemCondition } = useSellConsignmentStore()
  const Member = new MemberApiRepository()
  const wrapperHelperText = (helperText: string, state: string) => {
    return (
      <div className="-mt-4 flex flex-col items-end gap-sm lg:flex-row">
        <div className="w-full lg:w-[96px]" />
        <div className="flex w-full items-center gap-xxs text-left lg:w-auto lg:grow">
          <Text
            size="xs"
            state={
              state as
                | "danger"
                | "success"
                | "warning"
                | "disabled"
                | "primary"
                | "secondary"
            }
            type="regular"
          >
            {helperText}
          </Text>
        </div>
        <div className="flex w-full items-end lg:w-[109px]" />
      </div>
    )
  }
  const { data: itemSummary } = useItemSummary(selectedProduct.id)
  const getLowesAsk = (sizeId: string) => {
    if (itemCondition === "Brand New") {
      return (
        Number(
          itemSummary?.data.lowestAsk.standardBrandNewNoDefect[sizeId]
            ?.minUnitVal,
        ) ?? 0
      )
    }

    if (itemCondition === "") {
      return (
        itemSummary?.data.lowestAsk.expressBrandNewNoDefect[sizeId]
          ?.minUnitVal ?? 0
      )
    }

    return 0
  }

  const getHighestOffer = (sizeId: string) => {
    if (itemCondition === "Brand New") {
      const offer =
        itemSummary?.data.highestOffer.standardBrandNewNoDefect[sizeId]
      return offer ? Number(offer.amount) : 0
    }

    if (itemCondition === "") {
      const offer =
        itemSummary?.data.highestOffer.expressBrandNewNoDefect[sizeId]
      return offer ? Number(offer.amount) : 0
    }

    return 0
  }

  const getHelperText = useCallback(
    (price: number, id: string) => {
      const lowestAsk = formatPriceMinUnitVal(Number(getLowesAsk(id))) ?? 0
      const highestOffer = Number(getHighestOffer(id)) ?? 0
      const hasPrice = price > 0 && lowestAsk > 0

      switch (true) {
        case price % 10000 !== 0:
          return wrapperHelperText(
            "The amount entered must be a multiple of 10,000",
            "danger",
          )
        case price <= highestOffer && hasPrice:
          return wrapperHelperText(
            "Your ask cannot be lower than the highest offer",
            "danger",
          )
        case price > lowestAsk && hasPrice:
          return wrapperHelperText(
            "Your ask is higher than the lowest ask",
            "warning",
          )
        case (price === lowestAsk || price < lowestAsk) && hasPrice:
          return wrapperHelperText("You have the lowest ask", "success")
        default:
          return ""
      }
    },
    [getLowesAsk, getHighestOffer, wrapperHelperText],
  )

  const getVariantInputPrice = (price: number, id: string) => {
    const lowestAsk = formatPriceMinUnitVal(Number(getLowesAsk(id)))
    const highestOffer = Number(getHighestOffer(id))
    const hasPrice = price > 0 && lowestAsk > 0

    switch (true) {
      case price % 10000 !== 0:
        return {
          variant: "danger",
          icon: <IconDangerCircleBulk />,
          disabled: false,
        }
      case price <= highestOffer && price > 0:
        return {
          variant: "danger",
          icon: <IconDangerCircleBulk />,
          disabled: false,
        }
      case (price === lowestAsk || price < lowestAsk) && hasPrice:
        return {
          variant: "success",
          icon: <IconSuccessCircleBulk />,
          disabled: false,
        }

      case price > lowestAsk && hasPrice:
        return {
          variant: "warning",
          icon: <IconWarningCircleBulk />,
          disabled: false,
        }
      default:
        return {
          variant: "default",
          icon: null,
          disabled: false,
        }
    }
  }

  const handleInputChange = (
    e: React.FormEvent<HTMLInputElement>,
    sizeId: string,
  ) => {
    const formattedValue = formatNumberWithSeparator(
      Number(e.currentTarget.value.replace(/,/g, "")),
      ",",
    )
    setValues((prevValues) => ({
      ...prevValues,
      [sizeId]: formattedValue,
    }))
  }
  const { data: member } = useQuery({
    queryKey: ["getMember", "member"],
    queryFn: () => {
      return Member.getByMy()
    },
  })
  const { getMyPlatformFee } = useGetPlatformFee({
    memberId: member?.id,
    categoryId: selectedProduct?.category?.id,
  })
  return (
    <>
      <div className="flex flex-col items-end gap-sm lg:flex-row">
        <div className="flex h-[43px] w-full justify-start gap-xs rounded-sm border border-solid border-gray-w-80 bg-white p-sm lg:w-[96px]">
          <Text size="sm" state="primary" type="bold">
            Size
          </Text>
          <Text size="sm" state="primary" type="regular">
            {size.label.toUpperCase()}
          </Text>
        </div>
        <div className="w-full lg:w-auto lg:grow">
          <Input
            prefix="IDR"
            onChange={(e) => {
              handleInputChange(e, size.id)
              handlePriceChange(
                Number(e.currentTarget.value.replace(/,/g, "")),
                size.id,
              )
            }}
            onKeyDown={handleNumericInput}
            value={values[size.id] || ""}
            inputType="text"
            variant={
              getVariantInputPrice(size.price, size.id)
                ?.variant as TInputProps["variant"]
            }
            rightIcon={getVariantInputPrice(size.price, size.id)?.icon}
            disabled={getVariantInputPrice(size.price, size.id)?.disabled}
          />
        </div>
        <div className="flex w-full items-end lg:w-[109px]">
          <InputQuantity
            onChange={(e) => {
              handleQuantityChange(Number(e), size.id)
            }}
            type="number"
            className="w-full !gap-y-0 *:!w-full lg:*:!w-fit"
          />
        </div>
      </div>
      {getHelperText(size.price, size.id)}
      {size.price !== 0 && (
        <div className="-mt-3 flex flex-col items-end gap-sm lg:flex-row">
          <div className="w-full lg:w-[96px]" />
          <div className="flex w-full items-center gap-xxs text-left lg:w-auto lg:grow">
            <Text size="xs" state="primary" type="regular">
              You will earn{" "}
              {formatCurrency(
                size.price * (1 - (getMyPlatformFee()?.fee as number)),
                ",",
                "IDR",
              )}{" "}
              ({(getMyPlatformFee()?.fee as number) * 100}% Platform Fee)
            </Text>
            <Tooltip
              direction="right"
              classNameContent="!w-[200px]"
              text="We charge a small fee per sale to improve our platform. As you sell more, you can earn more Seller Points, which can lower your fees."
            >
              <IconInfoCircleBold />
            </Tooltip>
          </div>
          <div className="flex w-full items-end lg:w-[109px]" />
        </div>
      )}
      <Divider orientation="horizontal" />
    </>
  )
}

export default ListSize
