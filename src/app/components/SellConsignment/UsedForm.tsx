/* eslint-disable max-lines-per-function */
"use client"

import React, { useEffect, useState } from "react"
import {
  Chip,
  IconDangerCircleBulk,
  IconSuccessCircleBulk,
  Label,
  Text,
  TInputProps,
} from "@kickavenue/ui/components"
import { useQuery } from "@tanstack/react-query"
import { TItemOption } from "@kickavenue/ui/dist/src/components/ComboBox/ComboBox.type"

import ComboBox from "@shared/Form/ComboBox"
import Textarea from "@shared/Form/Textarea"
import Input from "@shared/Form/Input"
import { useSellConsignmentStore } from "stores/sellConsignmentStore"
import { SizeChartApiRepository } from "@infrastructure/repositories/sizeChartApiRepository"
import { useSellerListingStore } from "stores/useSellerListing"
import { formatCurrency, formatNumberWithSeparator } from "@utils/separator"
import { handleNumericInput } from "@utils/misc"
import { MemberApiRepository } from "@infrastructure/repositories/memberApiRepository"
import useGetPlatformFee from "@app/hooks/useGetPlatformFee"

import { defectDetails, packagingConditions } from "./sell.consignment.utils"
import { UploadImageField } from "./UploadImageField"

const UsedForm = () => {
  const { itemCondition, selectedProduct } = useSellConsignmentStore()
  const [value, setValue] = useState("")
  const [size, setSize] = useState<(TItemOption & { id?: string }) | null>(null)
  const [packaging, setPackaging] = useState<TItemOption | null>(null)
  const [defectDetail, setDefectDetail] = useState<string[]>([])
  const price = Number(value.replace(/,/g, ""))
  const isValidPrice = price % 10000 === 0

  const handleOnchange = (e: React.FormEvent<HTMLInputElement>) => {
    setValue(
      formatNumberWithSeparator(
        Number(e.currentTarget.value.replace(/,/g, "")),
        ",",
      ),
    )
  }

  const getHelperText = () => {
    if (price === 0) return ""
    if (isValidPrice) return "You have the lowest ask"
    if (!isValidPrice) return "The amount entered must be a multiple of 10,000"
    return ""
  }

  const getVariantInputPrice = () => {
    // Don't show validation if no price entered
    if (price === 0) return null
    if (isValidPrice)
      return {
        variant: "success",
        icon: <IconSuccessCircleBulk />,
      }
    if (!isValidPrice)
      return {
        variant: "danger",
        icon: <IconDangerCircleBulk />,
      }
    return null
  }

  const { setField } = useSellerListingStore()
  const isShowPlafromFee = value !== ""
  const itemConditionLabel =
    itemCondition === "99% Perfect" ? "Brand New with Defect" : itemCondition

  const handleDefectDetail = (label: string) => {
    setDefectDetail((prevDetails) =>
      prevDetails.includes(label)
        ? prevDetails.filter((item) => item !== label)
        : [...prevDetails, label],
    )
  }

  const SizeChart = new SizeChartApiRepository()
  const { data } = useQuery({
    queryKey: ["getSizeById", selectedProduct.id],
    queryFn: () => {
      if (selectedProduct.sizeChartId) {
        return SizeChart.getById(selectedProduct.sizeChartId)
      }
    },
  })

  const Member = new MemberApiRepository()
  const { data: member } = useQuery({
    queryKey: ["getMember", "member"],
    queryFn: () => {
      return Member.getByMy()
    },
  })

  const { getMyPlatformFee } = useGetPlatformFee({
    memberId: member?.id,
    categoryId: selectedProduct?.category?.id,
  })

  const calculateEarnings = () => {
    const price = Number(value.replace(/,/g, ""))
    const fee = getMyPlatformFee()?.fee ?? 0
    const earnings = price * (1 - fee)
    const feePercentage = fee

    return {
      earnings: formatCurrency(earnings, ".", "IDR"),
      feePercentage: feePercentage.toFixed(3),
    }
  }

  const sizeList = data?.size?.map((item: any) => ({
    id: item.id,
    label: `US ${item.us}`,
    value: item.us,
  }))

  useEffect(() => {
    setField("sizeId", Number(size?.id))
  }, [setField, size])

  useEffect(() => {
    setField(
      "packagingCondition",
      packaging?.value.replace(/\s+/g, "_").toUpperCase(),
    )
  }, [packaging?.value, setField])

  useEffect(() => {
    setField(
      "defectDetail",
      defectDetail.map((item) => ({
        type: item,
        name: item,
        info: item,
      })),
    )
  }, [defectDetail, setField])

  const renderDefectDetailError = () => {
    if (
      (itemCondition === "99% Perfect" || itemCondition === "Used") &&
      defectDetail.length === 0
    ) {
      return (
        <Text size="xs" state="danger" type="regular">
          Please select at least one defect detail
        </Text>
      )
    }
    return null
  }

  const renderPlatformFee = () => {
    if (isShowPlafromFee) {
      return (
        <Text size="xs" state="primary" type="regular">
          You will earn {calculateEarnings().earnings} (
          {calculateEarnings().feePercentage}% Platform Fee)
        </Text>
      )
    }
    return null
  }

  const defectHelperText =
    itemCondition === "Brand New" ? "(optional)" : "(required)"
  const defectState = itemCondition === "Brand New" ? "optional" : "required"

  return (
    <div className="mb-24 flex flex-col gap-y-lg text-left">
      <ComboBox
        selected={size}
        items={sizeList}
        setSelected={setSize}
        state="required"
        label="Size"
        placeholder="Select Size"
      />
      <Input value={itemConditionLabel} label="Item Condition" disabled />
      <ComboBox
        selected={packaging}
        setSelected={setPackaging}
        items={packagingConditions}
        state="required"
        label="Packaging Condition"
        placeholder="Packaging Condition"
      />
      <div className="flex flex-col gap-y-sm">
        <Label
          helperText={defectHelperText}
          size="sm"
          state={defectState}
          type="default"
        >
          Defect Details
        </Label>
        {renderDefectDetailError()}
        <div className="flex flex-wrap gap-sm">
          {defectDetails.map((item) => (
            <Chip
              onClick={() => handleDefectDetail(item.label)}
              isSelected={defectDetail.includes(item.label)}
              key={item.id}
              size="md"
              className="cursor-pointer"
            >
              {item.label}
            </Chip>
          ))}
        </div>
      </div>
      <div className="text-left">
        <div className="flex items-center gap-xxs">
          <Label
            helperText="(Minimum 5 images, maximum 10 images)"
            size="sm"
            state="required"
            type="default"
          >
            Upload Image
          </Label>
          <Text size="xs" state="secondary" type="regular">
            (Minimum 5 images, maximum 10 images)
          </Text>
        </div>
        <Text size="xs" state="secondary" type="regular" className="pt-xs">
          Take clear, well-lit photos of the item from multiple angles. Include
          close-up shots of any defects or damages to accurately represent the
          item&apos;s condition.
        </Text>
      </div>
      <UploadImageField />
      <Textarea
        onChange={(e) => setField("note", e.currentTarget.value)}
        label="Description"
        state="optional"
        placeholder="Clearly describe any defects or signs of wear so buyers can fully understand the Item's condition."
      />
      <Input
        prefix="IDR"
        placeholder="Input Price"
        state="required"
        label="Price"
        inputType="text"
        value={value}
        onKeyDown={handleNumericInput}
        onChange={(e) => {
          handleOnchange(e)
          setField(
            "sellingPrice",
            Number(e.currentTarget.value.replace(/,/g, "")),
          )
        }}
        variant={getVariantInputPrice()?.variant as TInputProps["variant"]}
        rightIcon={getVariantInputPrice()?.icon}
        helperText={getHelperText()}
      />
      {renderPlatformFee()}
    </div>
  )
}

export default UsedForm
