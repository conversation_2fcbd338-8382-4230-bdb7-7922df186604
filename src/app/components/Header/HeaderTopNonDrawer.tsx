"use client"

import { Logo } from "@kickavenue/ui/components/icons"
import Link from "next/link"
import { cx } from "class-variance-authority"

import SearchInput from "@components/shared/SearchInput"
import useHeaderTopNonDrawer from "@hooks/useHeaderTopNonDrawer"

import AvatarProfile from "./AvatarProfile"

const navLinks = [
  { label: "Home", href: "/" },
  { label: "Market", href: "/search" },
  { label: "Sell", href: "/sell-consignment" },
]

const HeaderTopNonDrawer = () => {
  const {
    ref,
    searchKeyword,
    setSearchKeyword,
    setShowExpandedSearch,
    handleInputChange,
  } = useHeaderTopNonDrawer()

  return (
    <div className="flex justify-center bg-white">
      <div
        className={cx(
          "hidden items-center gap-lg p-sm md:flex md:px-xxl xl:px-0",
          "w-full xl:max-w-[calc(100vw-80px)]",
        )}
      >
        <Link href="/">
          <Logo className="!w-[188px]" />
        </Link>
        <div className="w-full">
          <SearchInput
            size="sm"
            placeholder="1,000,000+ authentic items here"
            onFocus={() => setShowExpandedSearch(true)}
            onChange={handleInputChange}
            onClearText={() => setSearchKeyword("")}
            value={searchKeyword}
            ref={ref}
          />
        </div>
        <div className="flex items-center justify-end gap-lg">
          <nav>
            <ul className="flex gap-lg text-base">
              {navLinks.map((link) => (
                <li key={link.label} className="leading-none">
                  <Link href={link.href}>{link.label}</Link>
                </li>
              ))}
            </ul>
          </nav>
          <AvatarProfile />
        </div>
      </div>
    </div>
  )
}

export default HeaderTopNonDrawer
