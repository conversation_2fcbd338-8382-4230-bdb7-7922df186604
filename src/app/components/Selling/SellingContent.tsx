"use client"

import { use<PERSON>ara<PERSON> } from "next/navigation"
import { useEffect } from "react"

import Loading from "@components/shared/Loading"
import useFetchProductById from "@app/hooks/useFetchProductById"
import { useProductStore } from "stores/productStore"
import useFetchItemSummary from "@app/hooks/useFetchItemSummary"
import { useSellingStore } from "stores/sellingStore"
import useGetSizeById from "@app/hooks/useGetSizeById"
import { SellingSegmented } from "types/selling.type"
import { QueryStatus } from "types/network.type"
import { buildQueryStringBySortBy, getStripAmount } from "@utils/misc"
import { SellerListing } from "types/sellerListing"
import { FormFieldConstant } from "@constants/formField"
import { useMemberStore } from "stores/memberStore"
import useFetchOffers from "@app/hooks/useFetchOffers"
import { MiscConstant } from "@constants/misc"
import { TOffer } from "types/offer.type"
import useToast from "@app/hooks/useToast"
import { getApiErrorMessage } from "@utils/network"
import { TPlatformFee } from "types/fee.type"
import useGetPlatformFee from "@app/hooks/useGetPlatformFee"

import SellingSegmentedSection from "./SellingSegmentedSection"
import usePlaceAskForm from "./hooks/usePlaceAskForm"
import PlaceAskForm from "./PlaceAskForm"
import SellingNowForm from "./SellingNowForm"
import { getHighestOfferAmount, isEmptyHighestOffer } from "./utils/sell.utils"
import useSubmitSellNow from "./hooks/useSubmitSellNow"
import SellingAskSuccess from "./SellingAskSuccess"
import SellingNowSuccess from "./SellingNowSuccess"

const { Ask, Sell } = SellingSegmented
const { ASK_PRICE } = FormFieldConstant.SELLING_PLACE_ASK

const { PAGE, PAGE_SIZE } = MiscConstant.PAGING_DEFAULT

// eslint-disable-next-line max-lines-per-function
const SellingContent = () => {
  const { setProductDetail, detail } = useProductStore()
  const {
    setItemSummary,
    setHighestOffer,
    setSize,
    setPlatformFee,
    setSegmented,
    highestOffer,
    size,
    segmented,
  } = useSellingStore()
  const { member } = useMemberStore()
  const { sizeid, id } = useParams<{ sizeid: string; id: string }>()
  const productId = Number(id)

  const { setShowToast } = useToast()

  const { isLoading } = useFetchProductById({
    id: Number(productId),
    onSuccess: (product) => {
      setProductDetail(product)
    },
  })

  useGetSizeById({
    id: Number(sizeid),
    onSuccess: (size) => {
      setSize(size)
    },
  })

  const { isLoading: isLoadingItemSummary } = useFetchItemSummary(
    Number(productId),
    (data) => {
      setItemSummary(data)
    },
    Boolean(size?.id),
  )

  useFetchOffers({
    filter: {
      itemId: productId,
      sizeId: [Number(sizeid)],
      page: PAGE,
      pageSize: PAGE_SIZE,
      sortBy: buildQueryStringBySortBy("amount,DESC"),
      sort: [],
    },
    onSuccess: (data) => {
      const highestOffer = data.content?.[0] as TOffer
      setHighestOffer(highestOffer)
      setSegmented(isEmptyHighestOffer(highestOffer) ? Ask : Sell)
    },
  })

  const { getMyPlatformFee, data } = useGetPlatformFee({
    memberId: member?.id,
    categoryId: detail.category?.id,
  })

  useEffect(() => {
    if (data) {
      setPlatformFee(getMyPlatformFee() as TPlatformFee)
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [data])

  const {
    form,
    disabled,
    isPending: isPendingSubmitAsk,
    onFormValid,
    status: createStatus,
  } = usePlaceAskForm()

  const {
    mutate: submitSellNow,
    isPending: isPendingSubmitSellNow,
    status: submitSellNowStatus,
    data: sellNowData,
  } = useSubmitSellNow({
    onError(error) {
      setShowToast(true, getApiErrorMessage(error), "danger")
    },
  })

  if (
    isLoading ||
    isLoadingItemSummary ||
    isPendingSubmitAsk ||
    isPendingSubmitSellNow
  ) {
    return <Loading />
  }

  if (createStatus === QueryStatus.Success) {
    return (
      <SellingAskSuccess
        price={Number(form.getValues(ASK_PRICE.KEY as keyof SellerListing))}
      />
    )
  }

  if (submitSellNowStatus === QueryStatus.Success) {
    return (
      <SellingNowSuccess
        price={getStripAmount(highestOffer?.amount)}
        invoiceNumber={sellNowData?.invoiceNumber}
      />
    )
  }

  return (
    <>
      <SellingSegmentedSection />
      <div className="flex min-h-screen w-screen justify-center bg-gray-w-95 px-sm py-xl">
        {segmented === Sell && !isEmptyHighestOffer(highestOffer) && (
          <SellingNowForm
            onSubmit={() => submitSellNow(highestOffer?.id as number)}
            disabled={
              getHighestOfferAmount(highestOffer) <= 0 || isPendingSubmitSellNow
            }
          />
        )}
        {segmented === Ask && (
          <PlaceAskForm
            form={form}
            disabled={disabled}
            onFormValid={onFormValid}
          />
        )}
      </div>
    </>
  )
}

export default SellingContent
