import { Divider, Text } from "@kickavenue/ui/components"
import ProductImage from "@kickavenue/ui/components/ProductImage"
import { useRouter } from "next/navigation"

import ClickableDiv from "@components/shared/ClickableDiv"
import { getProductImageUrl } from "@utils/misc"
import { useMiscStore } from "stores/miscStore"
import { Product } from "types/product.type"

export interface TopResultItemProps {
  product: Product
}

const TopResultItem = ({ product }: TopResultItemProps) => {
  const router = useRouter()
  const { setShowExpandedSearch } = useMiscStore()
  const handleItemClick = () => {
    setShowExpandedSearch(false)
    router.push(`/product/${product?.id}`)
  }
  return (
    <ClickableDiv
      keyDownHandler={handleItemClick}
      onClick={handleItemClick}
      className="flex w-full flex-col gap-y-base"
    >
      <div className="flex items-center gap-sm rounded-lg hover:bg-gray-w-95">
        <ProductImage
          imageProps={{
            src: getProductImageUrl(product),
            alt: product?.name,
            width: 82,
            height: 82,
          }}
        />
        <div className="flex h-full flex-col justify-center">
          <Text size="sm" type="regular" state="primary">
            {product?.brands?.[0]?.name || ""}
          </Text>
          <Text size="base" type="bold" state="primary">
            {product?.name || ""}
          </Text>
        </div>
      </div>

      <Divider orientation="horizontal" type="solid" />
    </ClickableDiv>
  )
}

export default TopResultItem
