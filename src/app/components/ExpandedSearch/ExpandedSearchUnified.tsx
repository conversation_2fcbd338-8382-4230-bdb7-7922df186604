"use client"

import {
  IconCloseOutline,
  IconSearchOutline,
  Input,
} from "@kickavenue/ui/components"
import { cx } from "class-variance-authority"
import { FormEvent, useEffect, useMemo } from "react"

import useExpandedSearch from "@app/hooks/useExpandedSearch"
import InputCloseIcon from "@shared/InputCloseIcon"
import { useMiscStore } from "stores/miscStore"

import ExpandedSearchContent from "./ExpandedSearchContent"
import SearchResultPreview from "./SearchResultPreview"

const ExpandedSearchUnified = () => {
  const {
    showExpandedSearch,
    showSearchResultPreview,
    setShowSearchResultPreview,
    handleClose,
    handleKeyDown,
  } = useExpandedSearch()
  const { setSearchKeyword, searchKeyword } = useMiscStore()

  const renderContent = showSearchResultPreview ? (
    <SearchResultPreview />
  ) : (
    <ExpandedSearchContent />
  )

  const handleInputChange = (e: FormEvent<HTMLInputElement>) => {
    const value = (e.target as HTMLInputElement).value
    setSearchKeyword(value)
  }

  useEffect(() => {
    setShowSearchResultPreview(searchKeyword.length > 3)
  }, [searchKeyword, setShowSearchResultPreview])

  // Responsive styles that combine both mobile fullscreen and desktop overlay
  const containerStyles = useMemo(() => {
    return cx(
      "absolute inset-0",
      // Mobile fullscreen styles (default)
      "z-40 overflow-y-hidden bg-white transition-all duration-[0.3s] ease-in-out",
      showExpandedSearch && "translate-y-0 opacity-100",
      !showExpandedSearch && "-translate-y-full overflow-hidden opacity-0",
      // Desktop/tablet overlay styles (md and up)
      "md:z-20 min-h-screen md:bg-transparent md:duration-500",
      showExpandedSearch
        ? "md:pointer-events-auto md:translate-y-[65px] md:opacity-100"
        : "md:pointer-events-none md:-translate-y-full md:opacity-0",
    )
  }, [showExpandedSearch])

  const backdropStyles = useMemo(() => {
    return cx(
      // Hidden on mobile, visible on desktop/tablet
      "hidden md:block size-full h-[50vh] bg-black-dim-40",
      "transition-all duration-300",
      showExpandedSearch ? "opacity-100" : "opacity-0",
    )
  }, [showExpandedSearch])

  return (
    <div className={containerStyles}>
      {/* Mobile search header - only visible on mobile */}
      <div className="sticky top-0 flex items-center gap-sm bg-white p-base md:hidden">
        <Input
          leftIcon={<IconSearchOutline />}
          rightIcon={
            <InputCloseIcon
              text={searchKeyword}
              onClick={() => setSearchKeyword("")}
            />
          }
          size="sm"
          containerInputProps={{
            className: "!bg-gray-w-95 !border-none",
          }}
          onChange={handleInputChange}
          value={searchKeyword}
        />
        <IconCloseOutline width={24} height={24} onClick={handleClose} />
      </div>

      {/* Content area */}
      {renderContent}

      {/* Desktop/tablet backdrop - only visible on md and up */}
      <div
        className={backdropStyles}
        onClick={handleClose}
        onFocus={handleClose}
        onKeyDown={handleKeyDown}
        aria-label="Close expanded search"
      />
    </div>
  )
}

export default ExpandedSearchUnified
