import NavigationOption from "@kickavenue/ui/components/NavigationOption"
import { useQuery } from "@tanstack/react-query"
import { debounce } from "lodash"
import { useRouter } from "next/navigation"
import { useEffect, useState } from "react"
import { cx } from "class-variance-authority"

import { SearchPreview } from "@application/usecases/searchPreview"
import { QueryKeysConstant } from "@constants/queryKeys.constant"
import { ProductApiRepository } from "@infrastructure/repositories/productApiRepository"
import { getApiErrorMessage } from "@utils/network"
import { useMiscStore } from "stores/miscStore"
import { QueryStatus } from "types/network.type"
import { TCategoryPreview } from "types/productSearchPreview"

import SearchStatus from "./SearchStatus"

const { SEARCH_PER_CATEGORY } = QueryKeysConstant

const SearchResultPreviewCategory = () => {
  const r = new ProductApiRepository()
  const u = new SearchPreview(r)

  const { searchKeyword, setShowExpandedSearch } = useMiscStore()
  const router = useRouter()
  const [debouncedKeyword, setDebouncedKeyword] = useState("")

  useEffect(() => {
    const handler = debounce(() => {
      setDebouncedKeyword(searchKeyword)
    }, 500)
    handler()
    return () => {
      handler.cancel()
    }
  }, [searchKeyword])

  const query = useQuery({
    queryKey: [SEARCH_PER_CATEGORY, debouncedKeyword],
    queryFn: () => u.execute({ search: debouncedKeyword }),
    enabled: debouncedKeyword?.length > 3,
    retry: false,
  })

  const handleNavOptionClick = (category: TCategoryPreview) => {
    setShowExpandedSearch(false)
    router.push(`/search?keyword=${searchKeyword}&category=${category.name}`)
  }

  const error = getApiErrorMessage(query.error)
  if (error) {
    return <SearchStatus text="No results found" />
  }

  if (query?.isLoading || query?.status === QueryStatus.Pending) {
    return <SearchStatus spinner />
  }

  const categories =
    query?.data?.categories || ({} as Record<number, TCategoryPreview>)
  const entries = Object.entries(categories)

  return (
    <div className="flex flex-col gap-y-base">
      {entries.map(([key, value]) => (
        <div key={key} className="mx-auto w-full xl:max-w-[calc(100vw-80px)]">
          <NavigationOption
            title={`${searchKeyword} in ${value.name}`}
            subTitle={`${value.count} results`}
            withArrowRight
            className={cx("cursor-pointer !p-0")}
            onClick={() => handleNavOptionClick(value)}
          />
        </div>
      ))}
    </div>
  )
}

export default SearchResultPreviewCategory
