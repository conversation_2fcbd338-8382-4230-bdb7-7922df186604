/* eslint-disable @typescript-eslint/no-extraneous-class */
export class QueryKeysConstant {
  static readonly GET_MENU_WIZARD = "getMenuWizard"
  static readonly GET_LOGGED_IN_MEMBER = "getLoggedInMember"
  static readonly GET_SELLING_IN_PROGRESS = "getSellingInProgress"
  static readonly GET_SELLING_HISTORY = "getSellingHistory"
  static readonly GET_CONSIGNMENT_ACTIVE = "getConsignmentActive"
  static readonly GET_CONSIGNMENT_IN_PROGRESS = "getConsignmentInProgress"
  static readonly GET_CONSIGNMENT_HISTORY = "getConsignmentHistory"
  static readonly GET_CATEGORIES_BY_NAME = "getCategoriesByName"
  static readonly GET_SUBCATEGORIES_BY_NAME = "getSubCategoriesByName"
  static readonly GET_PRODUCT_HISTORICAL = "getProductHistorical"
  static readonly GET_PRODUCT_BY_ID = "getProductById"
  static readonly GET_SELLING_CURRENT = "getSellingCurrent"
  static readonly GET_CONSIGNMENT_SUMMARY = "getConsignmentSummary"
  static readonly UPDATE_HOLIDAY_MODE = "updateHolidayMode"
  static readonly GET_SELLING_SUMMARY = "getSellingSummary"
  static readonly GET_BUYING_CURRENT = "getBuyingCurrent"
  static readonly GET_BUYING_OFFERS = "getBuyingOffers"
  static readonly GET_MY_BUYING_DASHBOARD = "getMyBuyingDashboard"
  static readonly SEARCH_PRODUCTS = "searchProducts"
  static readonly SEARCH_PER_CATEGORY = "searchPerCategory"
  static readonly SEARCH_TOP_RESULT = "searchTopResult"
  static readonly GET_MY_ADDRESS = "getMyAddress"
  static readonly GET_ADDRESS_BY_ID = "getAddressById"
  static readonly GET_VOUCHER_ACTIVE = "getVoucherActive"
  static readonly GET_VOUCHER_USAGE = "getVoucherUsage"
  static readonly GET_INVITED_FRIENDS = "getInvitedFriends"
  static readonly GET_BANK_ACCOUNT = "getBankAccount"
  static readonly GET_BANK_BY_ID = "getBankById"
  static readonly GET_SELLING_PLATFORM_FEE = "getSellingPlatformFee"
  static readonly GET_ALL_MY_BANK_ACCOUNT = "getAllMyBankAccount"
  static readonly GET_MY_BANK_ACCOUNT_BY_ID = "getMyBankAccountById"
  static readonly CREATE_OTP = "createOtp"
  static readonly SUBMIT_SELLER_REGISTRATION = "submitSellerRegistration"
  static readonly GET_MY_MEMBER = "getMyMember"
  static readonly GET_MY_WISHLIST = "getMyWishlist"
  static readonly GET_ALL_ADDRESS = "getAllAddress"
  static readonly GET_PAYMENT_FEE = "getPaymentFee"
  static readonly GET_ALL_SELLER_REGISTRATION = "getAllSellerRegistration"
  static readonly UPDATE_BANK_INFO = "updateBankInfo"
  static readonly GET_ALL_BANK_INFO = "getAllBankInfo"
  static readonly GET_BANK_INFO_BY_ID = "getBankInfoById"
  static readonly GET_BUYING_SUMMARY = "getBuyingSummary"
  static readonly GET_CURRENT_FAQ = "getCurrentFaq"
  static readonly GET_ALL_FAQ_CONTENT = "getAllFaqContent"
  static readonly GET_CURRENT_ARTICLE = "getCurrentArticle"
  static readonly GET_SALE_DETAIL = "getSaleDetail"
  static readonly GET_SELLER_LISTING_BY_ID = "getSellerListingById"
  static readonly GET_SELLER_LISTINGS = "getSellerListings"
  static readonly GET_ALL_SIZE_CHART = "getAllSizeChart"
  static readonly GET_ITEM_SUMMARY = "getItemSummary"
  static readonly GET_SIZE_CHART_BY_ID = "getSizeChartById"
  static readonly WISHLIST_COLLECTIONS = "wishlistCollections"
  static readonly GET_MEMBER_PLATFORM_FEE = "getMemberPlatformFee"
  static readonly GET_UNIQUE_SIZE = "getUniqueSize"
  static readonly CREATE_BANK_ACCOUNT = "createBankAccount"
  static readonly GET_LISTING_ITEM_BY_ID = "getListingItemById"
  static readonly GET_SIZE_BY_ID = "getSizeById"
  static readonly GET_MY_BALANCE = "getMyBalance"
  static readonly GET_ADDONS = "getAddons"
  static readonly GET_MY_VOUCHER = "getMyVoucher"
  static readonly GET_PAYMENT_METHODS = "getPaymentMethods"
  static readonly GET_FEE_RATES = "getFeeRates"
  static readonly GET_FEE_BY_ID = "getFeeById"
  static readonly GET_TRANSACTION = "getTransaction"
  static readonly GET_TRANSACTION_DETAIL = "getTransactionDetail"
  static readonly GET_SELLER_LISTINGS_BY_IDS = "getSellerListingsByIds"
  static readonly GET_STATUS_RAFFLE = "getStatusRaffle"
  static readonly GET_SELLER_STOCK_BY_ID = "getSellerStockById"
  static readonly GET_SELLER_STOCKS = "getSellerStocks"
  static readonly GET_SELLER_TRANSACTION_BY_ID = "getSellerTransactionById"
  static readonly GET_OFFER_BY_ID = "getOfferById"
  static readonly GET_BUYING_PENDING = "getBuyingPending"
  static readonly GET_OFFERS = "getOffers"
  static readonly GET_MY_TX_DETAILS = "getMyTxDetails"
  static readonly GET_MY_OFFERS = "getMyOffers"
  static readonly GET_MY_PENDING_BUYING = "getMyPendingBuying"
  static readonly GET_TOPUP_DATA = "getTopUpData"
  static readonly GET_MY_DISBURSEMENT = "getMyDisbursement"
  static readonly GET_MY_DISBURSEMENT_BY_ID = "getMyDisbursementById"
  static readonly GET_COLLECTION_BY_ID = "getCollectionById"
  static readonly GET_COUNTRIES = "getCollectionById"
  static readonly GET_PLATFORM_FEE = "getPlatformFee"
}
