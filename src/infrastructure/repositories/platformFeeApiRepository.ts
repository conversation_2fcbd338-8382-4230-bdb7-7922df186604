import { PlatformFeeRepository } from "@domain/interfaces/PlatformFeeRepository"
import { TPlatformFee } from "types/fee.type"
import { platformApi } from "@infrastructure/api/platformFeeApi"

export class PlatformFeeApiRepository implements PlatformFeeRepository {
  async getPlatformFee(id: number): Promise<TPlatformFee> {
    const response = await platformApi.getPlatformFee(id)
    return response.data as TPlatformFee
  }

  async getMyPlatformFee(): Promise<TPlatformFee[]> {
    const response = await platformApi.getMy()
    return response.data as TPlatformFee[]
  }
}
