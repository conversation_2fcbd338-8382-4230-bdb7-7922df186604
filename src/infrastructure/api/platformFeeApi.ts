import { convertToCamelCase } from "@utils/misc"
import { httpClient } from "@infrastructure/providers/httpClient"
import { TApiResponse } from "types/apiResponse.type"
import { TPlatformFee } from "types/fee.type"

export const platformApi = {
  getPlatformFee: async (id: number): Promise<TApiResponse<TPlatformFee>> => {
    const response = await httpClient.get(
      `/member/platform-fee/get/member/${id}`,
    )
    const apiResponse = convertToCamelCase(
      response.data,
    ) as TApiResponse<TPlatformFee>
    return apiResponse
  },
  getMy: async (): Promise<TApiResponse<TPlatformFee[]>> => {
    const response = await httpClient.get(`/member/platform-fee/get/member/my`)
    const apiResponse = convertToCamelCase(response.data) as TApiResponse<
      TPlatformFee[]
    >
    return apiResponse
  },
}
